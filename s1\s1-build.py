import os
import json
import re
import requests
from requests.auth import HTTPBasicAuth

# 定义基本认证信息
username = 'OvaJBUAn643Wrdye8tX24La3CQM6hxTm5avQcW4MLQS1'
password = 'gvqmNpCLuNm5fH9IffTThhrahMICK5kis16g0smMLQS1'

# 定义 URL 的基准部分
base_url = "http://118.196.17.104:13951/serve/build?name="

# base_url = "http://127.0.0.1:8000/serve/build?name="

# # 遍历 data 目录下的文件夹
data_dir = 'data'
# for servername in os.listdir(data_dir):
#     server_path = os.path.join(data_dir, servername)
#     if os.path.isdir(server_path):
#         # 构建完整的 URL
#         url = f"{base_url}{servername}"

#         # 构建 config.json 文件路径
#         config_file_path = os.path.join(server_path, 'config.json')

#         # 读取 config.json 文件内容
#         if os.path.exists(config_file_path):
#             with open(config_file_path, 'r', encoding='utf-8') as f:
#                 config_data = json.load(f)

#             # 发送 POST 请求
#             response = requests.post(url, json=config_data, auth=HTTPBasicAuth(username, password))

#             # 打印响应
#             print(f"Server: {servername}, Status Code: {response.status_code}, Response: {response.text}")
#         else:
#             print(f"Config file not found for server: {servername}")

# 请在这里面输入
start_number = 1
end_number = 1


def is_servername_in_range(servername, start, end):
    # 使用正则表达式匹配服务器名称模式 ds 后面加数字
    match = re.match(r'^dscn5(\d+)$', servername)

    if not match:
        return False

    # 提取数字部分并转换为整数
    server_number = int(match.group(1))

    # 判断数字是否在范围内
    return start <= server_number <= end


def process_servers(data_dir, base_url, start, end, username, password):
    for servername in os.listdir(data_dir):
        if is_servername_in_range(servername, start, end):
            server_path = os.path.join(data_dir, servername)
            if os.path.isdir(server_path):
                # 构建完整的 URL
                url = f"{base_url}{servername}"

                # 构建 config.json 文件路径
                config_file_path = os.path.join(server_path, 'config.json')

                # 读取 config.json 文件内容
                if os.path.exists(config_file_path):
                    with open(config_file_path, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)

                    # 发送 POST 请求
                    response = requests.post(url, json=config_data, auth=HTTPBasicAuth(username, password))

                    # 打印响应
                    print(f"Server: {servername}, Status Code: {response.status_code}, Response: {response.text}")
                else:
                    print(f"Config file not found for server: {servername}")
        else:
            print(f"Server name {servername} 不在输入范围构建略过.")


process_servers(data_dir, base_url, start_number, end_number, username, password)
