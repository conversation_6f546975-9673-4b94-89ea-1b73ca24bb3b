import json
import os
import requests
import time
from datetime import datetime, timedelta

# 定义 URL
url = "http://*************:13988/api/serve/store"
# url = "http://192.168.2.242:9880/api/serve/store"

# 定义请求头
headers = {
    "Authorization": "Bearer AQAAAAFt_ThoJXDt1ykIxh8mHhJwVEoOhw",
    "Content-Type": "application/json"
}

# 定义多个游戏数据库配置
game_db_configs = [
    {
        "game_base_addr": "mysqlbdb1e936bd52.rds.ivolces.com:3306",
        "game_base_user": "root",
        "game_base_passwd": "UiN36HhQZscne2GZCe"
    }
    # 添加更多数据库配置
]

# 定义多个日志数据库配置
log_db_configs = [
    {
        "game_logs_addr": "mysqlbdb1e936bd52.rds.ivolces.com:3306",
        "game_logs_user": "root",
        "game_logs_passwd": "UiN36HhQZscne2GZCe"
    }
    # 添加更多日志数据库配置
]

# 调用函数批量发送请求，范围为 1 到 20
start_number = 1
end_number = 1

# 获取今天凌晨0点的时间戳
today_midnight = datetime.combine(datetime.today(), datetime.min.time()).timestamp()
# 获取今天凌晨0点的时间戳
today_midnight = datetime.combine(datetime.today(), datetime.min.time()).timestamp()


# 将 serv_id 转换为字符串并填充前导零
def pad_id(serv_id):
    return str(serv_id).zfill(3)


# 定义批量发送请求的函数
def send_post_requests(start_id, end_id):
    game_db_config_count = len(game_db_configs)
    log_db_config_count = len(log_db_configs)

    for serv_id in range(start_id, end_id + 1):
        if serv_id % 2 == 0:
            continue

        padded_id = pad_id(serv_id)

        # 获取当前时间
        now = datetime.now()

        # 设置为当天的0点
        today_at_zero = now.replace(hour=0, minute=0, second=0, microsecond=0)

        # 增加100天
        release_at = today_at_zero + timedelta(days=365)

        # 转换为时间戳
        release_at_timestamp = int(release_at.timestamp())

        # 当前时间戳加2小时
        release_at = release_at_timestamp

        # 选择游戏数据库配置
        game_db_config = game_db_configs[serv_id % game_db_config_count]

        # 选择日志数据库配置
        log_db_config = log_db_configs[serv_id % log_db_config_count]

        # # 获取明天日期的 0 点时间戳
        # tomorrow_midnight = datetime.combine(datetime.now().date() + timedelta(days=1), datetime.min.time())
        # launched_at = int(tomorrow_midnight.timestamp())
        # 获取今天·日期的 0 点时间戳
        # today_midnight = datetime.combine(datetime.now().date(), datetime.min.time())
        launched_at = int(release_at)

        # 将时间戳转换为所需格式
        opentime = datetime.fromtimestamp(launched_at).strftime("%Y-%m-%d %H:%M:%S")

        # 构建请求体
        payload = {
            "serv_id": serv_id,
            "serv_title": get_servername(serv_id),
            "release_at": release_at,
            "launched_at": launched_at,
            "agent_id": 2,
            "central_id": 1,
            "agent_key": f"dscn5{padded_id}",
            "serv_addr": f"118.196.17.104:{13000 + serv_id}",
            "game_base_addr": game_db_config["game_base_addr"],
            "game_base_user": game_db_config["game_base_user"],
            "game_base_passwd": game_db_config["game_base_passwd"],
            "game_base_db": f"dscn5_game_{padded_id}",
            "game_logs_addr": log_db_config["game_logs_addr"],
            "game_logs_user": log_db_config["game_logs_user"],
            "game_logs_passwd": log_db_config["game_logs_passwd"],
            "game_logs_db": f"dscn5_log_{padded_id}",
            "game_redis_addr": "*********:6379",
            "game_redis_passwd": "XaCn5fvcS1",
            "game_redis_db": 0 + serv_id,
            "serv_status": 4,
            "merged_to": 0,
            "reserve_id": serv_id + 1,
            "maximum": 3500
        }

        # 生成配置文件内容
        config = {
            "serverid": serv_id,
            "gamename": f"dscn5{padded_id}",
            "servername": get_servername(serv_id),
            "lang": 2,
            "wshost": f"0.0.0.0:{13000 + serv_id}",
            "opentime": opentime,
            "serverver": 0,
            "gameid": "90689",
            "appkey": "034c37d3be4409db53070250c33eb66c",
            "admincode": "alibabawansui",
            "gm": serv_id * 1000000,
            "tstype": 0,
            "network": {
                "maxplayer": 3500,
                "blackip": [],
                "whiteid": [],
                "msgfilter": True
            },
            "serverext": {
                "cachehost": "ws://127.0.0.1:9998/",
                "ismaster": True,
                "mastersvr": "*********:13901",
                "battlesvr": "*********:13940",
                "masterwarnclose": 1,
                "maxplayer": 3500,
                "numrecord": "http://*************:13988/index.php",
                "updatetime": 60,
                "recharegesvr": "http://*************:13988/order/new?zoneId=%d&orderTime=%s"
            },
            "database": {
                "dbid": serv_id,
                "dblog": f"{log_db_config['game_logs_user']}:{log_db_config['game_logs_passwd']}@tcp({log_db_config['game_logs_addr']})/dscn5_log_{padded_id}?loc=Local&timeout=3.14s",
                "dbuser": f"{game_db_config['game_base_user']}:{game_db_config['game_base_passwd']}@tcp({game_db_config['game_base_addr']})/dscn5_game_{padded_id}?loc=Local&timeout=3.14s",
                "maxdbconn": 255,
                "redis": "*********:6379",
                "redisauth": "XaCn5fvcS1",
                "redisdb": 0 + serv_id
            },
            "log": {
                "logconsole": False,
                "loglevel": 1,
                "maxfilenum": 63,
                "maxfilesize": 511,
                "sdk": False
            },
            "appconfig": []
        }

        # 创建文件夹和保存配置文件
        servername = f"dscn5{padded_id}"
        filepath = f"data/{servername}/config.json"
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)

        # 发送 POST 请求
        response = requests.post(url, json=payload, headers=headers)
        print(response)

        # 打印响应
        print(f"serv_id: {serv_id}, status_code: {response.status_code}, response: {response.json()}")

        # 如果响应成功，等待0.5秒再发送下一个请求
        if response.status_code == 200:
            time.sleep(0.5)


def get_servername(serv_id):
    # 定义serv_id到servername的映射  
    server_map = {
        1: "柱合训练1",
        2: "柱合训练2",
        3: "柱合训练3",
        4: "柱合训练4",
        5: "柱合训练5",
        6: "柱合训练6",

    }
    # 检查serv_id是否在映射中  
    servername = server_map.get(serv_id, None)

    # 如果servername存在，则返回它；否则，返回默认格式  
    if servername:
        return servername
    else:
        padded_id = pad_id(serv_id)
        return f"柱合之战{padded_id}服"


# 调用函数批量发送请求，范围为 1 到 20
send_post_requests(start_number, end_number)
