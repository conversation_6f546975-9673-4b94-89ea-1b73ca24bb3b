import os
import json
import requests
from requests.auth import HTTPBasicAuth


#这个是同步data下面json配置方法
#这个是同步data下面json配置方法
#这个是同步data下面json配置方法
#这个是同步data下面json配置方法
#这个是同步data下面json配置方法


# 定义基本认证信息
username = 'hzr9UHXDxEWFovxqIKLtOGMarxXENBScoT1hop8i'
password = 'NnLpMsuOZH40i8CEpuJ8LUMfkB43cjdweSzyn7aR'

# 定义 URL 的基准部分
base_url = "http://114.132.55.110:15941/serve/config?debug=1&name="



# base_url = "http://127.0.0.1:8000/serve/build?name="

# 遍历 data 目录下的文件夹
data_dir = 'data'
for servername in os.listdir(data_dir):
    server_path = os.path.join(data_dir, servername)
    if os.path.isdir(server_path):
        # 构建完整的 URL
        url = f"{base_url}{servername}"
        
        # 构建 config.json 文件路径
        config_file_path = os.path.join(server_path, 'config.json')
        
        # 读取 config.json 文件内容
        if os.path.exists(config_file_path):
            with open(config_file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 发送 POST 请求
            response = requests.post(url, json=config_data, auth=HTTPBasicAuth(username, password))
            
            # 打印响应
            print(f"Server: {servername}, Status Code: {response.status_code}, Response: {response.text}")
        else:
            print(f"Config file not found for server: {servername}")